import React from 'react';
import { useSortable } from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import type { DragHandleProps } from './types';

interface SortableDragHandleProps extends DragHandleProps {
	id: string;
	disabled?: boolean;
}

export const DragHandle = React.memo<SortableDragHandleProps>(
	({ id, disabled = false, className = '', 'aria-label': ariaLabel = 'Arrastar linha' }) => {
		const {
			attributes,
			listeners,
			setNodeRef,
			transform,
			transition,
			isDragging,
		} = useSortable({
			id,
			disabled,
		});

		const style = {
			transform: CSS.Transform.toString(transform),
			transition,
		};

		return (
			<div
				ref={setNodeRef}
				style={style}
				className={`
					inline-flex items-center justify-center
					w-6 h-6 cursor-grab active:cursor-grabbing
					text-gray-400 hover:text-gray-600 dark:text-gray-500 dark:hover:text-gray-300
					transition-colors duration-200
					${isDragging ? 'opacity-50' : ''}
					${disabled ? 'cursor-not-allowed opacity-30' : ''}
					${className}
				`}
				{...attributes}
				{...listeners}
				aria-label={ariaLabel}
				role="button"
				tabIndex={disabled ? -1 : 0}
			>
				<svg
					width="16"
					height="16"
					viewBox="0 0 16 16"
					fill="currentColor"
					className="pointer-events-none"
				>
					<path d="M3 6a1 1 0 1 1 0-2 1 1 0 0 1 0 2zM3 8a1 1 0 1 1 0-2 1 1 0 0 1 0 2zM4 11a1 1 0 1 0-2 0 1 1 0 0 0 2 0zM7 5a1 1 0 1 1 0-2 1 1 0 0 1 0 2zM7 8a1 1 0 1 1 0-2 1 1 0 0 1 0 2zM8 11a1 1 0 1 0-2 0 1 1 0 0 0 2 0z" />
				</svg>
			</div>
		);
	}
);

DragHandle.displayName = 'DragHandle';
