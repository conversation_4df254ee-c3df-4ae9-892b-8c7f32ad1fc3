import React from 'react';
import { useSortable } from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import { DragHandle } from './DragHandle';
import { TableCell } from './TableCell';
import type { TableRowProps } from './types';

export const TableRow = React.memo<TableRowProps<any>>(
	({ row, columns, index, isDragging, className = '', cellClassName }) => {
		const {
			attributes,
			listeners,
			setNodeRef,
			transform,
			transition,
			isDragging: isRowDragging,
		} = useSortable({
			id: row.id,
			disabled: row.disabled,
		});

		const style = {
			transform: CSS.Transform.toString(transform),
			transition,
		};

		const rowClassNames = typeof className === 'function' 
			? className(row, index) 
			: className;

		return (
			<div
				ref={setNodeRef}
				style={style}
				className={`
					grid grid-cols-[auto_1fr] gap-0
					bg-white dark:bg-gray-800
					hover:bg-gray-50 dark:hover:bg-gray-750
					transition-colors duration-200
					${isRowDragging ? 'opacity-50 shadow-lg z-10' : ''}
					${row.disabled ? 'opacity-60 cursor-not-allowed' : ''}
					${rowClassNames}
				`}
				role="row"
				aria-rowindex={index + 2} // +2 porque header é row 1
				{...attributes}
			>
				{/* Drag Handle Column */}
				<div className="flex items-center justify-center p-2 border-b border-gray-200 dark:border-gray-700">
					<DragHandle
						id={row.id}
						disabled={row.disabled}
						{...listeners}
					/>
				</div>

				{/* Data Columns */}
				<div 
					className="grid gap-0"
					style={{
						gridTemplateColumns: columns.map(col => 
							col.width || 'minmax(120px, 1fr)'
						).join(' ')
					}}
				>
					{columns.map((column) => {
						const cellClass = typeof cellClassName === 'function'
							? cellClassName(column, row)
							: cellClassName;

						return (
							<TableCell
								key={column.id}
								column={column}
								row={row}
								rowIndex={index}
								className={cellClass}
							/>
						);
					})}
				</div>
			</div>
		);
	}
);

TableRow.displayName = 'TableRow';
