import React from 'react';
import type { TableColumn } from './types';

interface TableHeaderProps<T> {
	columns: TableColumn<T>[];
	className?: string;
	stickyHeader?: boolean;
}

export const TableHeader = React.memo<TableHeaderProps<any>>(
	({ columns, className = '', stickyHeader = false }) => {
		return (
			<div
				className={`
					grid grid-cols-[auto_1fr] gap-0
					bg-gray-50 dark:bg-gray-900
					border-b-2 border-gray-200 dark:border-gray-600
					${stickyHeader ? 'sticky top-0 z-20' : ''}
					${className}
				`}
				role="row"
				aria-rowindex={1}
			>
				{/* Drag Handle Header */}
				<div className="flex items-center justify-center p-4 font-medium text-gray-700 dark:text-gray-300">
					<span className="sr-only">Controles de arrastar</span>
					<svg
						width="16"
						height="16"
						viewBox="0 0 16 16"
						fill="currentColor"
						className="opacity-40"
						aria-hidden="true"
					>
						<path d="M3 6a1 1 0 1 1 0-2 1 1 0 0 1 0 2zM3 8a1 1 0 1 1 0-2 1 1 0 0 1 0 2zM4 11a1 1 0 1 0-2 0 1 1 0 0 0 2 0zM7 5a1 1 0 1 1 0-2 1 1 0 0 1 0 2zM7 8a1 1 0 1 1 0-2 1 1 0 0 1 0 2zM8 11a1 1 0 1 0-2 0 1 1 0 0 0 2 0z" />
					</svg>
				</div>

				{/* Column Headers */}
				<div 
					className="grid gap-0"
					style={{
						gridTemplateColumns: columns.map(col => 
							col.width || 'minmax(120px, 1fr)'
						).join(' ')
					}}
				>
					{columns.map((column) => {
						const alignmentClass = {
							left: 'text-left',
							center: 'text-center',
							right: 'text-right',
						}[column.align || 'left'];

						return (
							<div
								key={column.id}
								className={`
									px-4 py-3
									font-medium text-gray-700 dark:text-gray-300
									border-b border-gray-200 dark:border-gray-700
									${alignmentClass}
								`}
								role="columnheader"
								style={{
									width: column.width,
									minWidth: column.minWidth,
									maxWidth: column.maxWidth,
								}}
							>
								{column.header}
							</div>
						);
					})}
				</div>
			</div>
		);
	}
);

TableHeader.displayName = 'TableHeader';
