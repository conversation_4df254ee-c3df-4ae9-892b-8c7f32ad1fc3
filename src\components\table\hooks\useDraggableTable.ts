import { DragEndEvent, DragStartEvent, KeyboardSensor, PointerSensor, TouchSensor, useSensor, useSensors } from "@dnd-kit/core";
import { sortableKeyboardCoordinates } from "@dnd-kit/sortable";
import { useCallback, useMemo } from "react";
import type { TableRow } from "../types";

export function useDraggableTable<T>({
	rows,
	onReorder,
	onDragStart,
	onDragEnd,
}: {
	rows: TableRow<T>[];
	onReorder: (activeId: string, overId: string) => void;
	onDragStart?: (id: string) => void;
	onDragEnd?: () => void;
}) {
	// Configuração otimizada de sensores para evitar conflitos
	const sensors = useSensors(
		useSensor(PointerSensor, {
			activationConstraint: {
				distance: 8, // Evita ativação acidental
			},
		}),
		useSensor(TouchSensor, {
			activationConstraint: {
				delay: 200,
				tolerance: 8,
			},
		}),
		useSensor(KeyboardSensor, {
			coordinateGetter: sortableKeyboardCoordinates,
		})
	);

	// Memoização dos IDs para evitar recriação desnecessária
	const itemIds = useMemo(() => rows.map(row => row.id), [rows]);

	const handleDragStart = useCallback(
		(event: DragStartEvent) => {
			const { active } = event;
			onDragStart?.(active.id as string);
		},
		[onDragStart]
	);

	const handleDragEnd = useCallback(
		(event: DragEndEvent) => {
			const { active, over } = event;

			if (over && active.id !== over.id) {
				onReorder(active.id as string, over.id as string);
			}

			onDragEnd?.();
		},
		[onReorder, onDragEnd]
	);

	return {
		sensors,
		itemIds,
		handleDragStart,
		handleDragEnd,
	};
}
