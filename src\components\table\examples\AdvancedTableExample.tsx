"use client";

import React, { useMemo } from 'react';
import { DraggableTable } from '../DraggableTable';
import { useAdvancedTable } from './useAdvancedTable';
import type { TableColumn, TableRowType } from '../types';

interface User {
	id: string;
	name: string;
	email: string;
	role: 'admin' | 'editor' | 'viewer';
	status: 'active' | 'inactive';
	createdAt: string;
	lastLogin: string;
	department: string;
}

// Dados de exemplo
const generateUsers = (count: number): TableRowType<User>[] => {
	const roles: User['role'][] = ['admin', 'editor', 'viewer'];
	const statuses: User['status'][] = ['active', 'inactive'];
	const departments = ['TI', 'RH', 'Vendas', 'Marketing', 'Financeiro'];

	return Array.from({ length: count }, (_, i) => ({
		id: `user-${i + 1}`,
		data: {
			id: `user-${i + 1}`,
			name: `Usuário ${i + 1}`,
			email: `usuario${i + 1}@empresa.com`,
			role: roles[Math.floor(Math.random() * roles.length)],
			status: statuses[Math.floor(Math.random() * statuses.length)],
			createdAt: new Date(Date.now() - Math.random() * 365 * 24 * 60 * 60 * 1000).toISOString(),
			lastLogin: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000).toLocaleDateString('pt-BR'),
			department: departments[Math.floor(Math.random() * departments.length)],
		},
	}));
};

export function AdvancedTableExample() {
	const initialData = useMemo(() => generateUsers(200), []);

	const {
		data,
		loading,
		pagination,
		sortConfig,
		filters,
		stats,
		handleSort,
		handleSearch,
		handleFilter,
		handlePageChange,
		handleRowsReorder,
		exportData,
		clearFilters,
		isSortable,
		getSortDirection,
	} = useAdvancedTable<User>({
		initialData,
		searchableFields: ['name', 'email', 'department'],
		sortableFields: ['name', 'email', 'createdAt', 'department'],
		pageSize: 25,
		enablePersistence: true,
		persistenceKey: 'users-table-state',
	});

	const columns: TableColumn<User>[] = useMemo(() => [
		{
			id: 'name',
			header: 'Nome',
			accessor: 'name',
			width: '200px',
			sortable: true,
			render: (value, row) => (
				<div className="flex items-center space-x-3">
					<div className="w-8 h-8 bg-indigo-100 dark:bg-indigo-900 rounded-full flex items-center justify-center">
						<span className="text-sm font-medium text-indigo-600 dark:text-indigo-400">
							{value.charAt(0)}
						</span>
					</div>
					<div>
						<div className="font-medium text-gray-900 dark:text-white">{value}</div>
						<div className="text-sm text-gray-500 dark:text-gray-400">{row.department}</div>
					</div>
				</div>
			),
		},
		{
			id: 'email',
			header: 'Email',
			accessor: 'email',
			width: '250px',
			sortable: true,
		},
		{
			id: 'role',
			header: 'Função',
			accessor: 'role',
			width: '120px',
			render: (value) => {
				const roleColors = {
					admin: 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200',
					editor: 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200',
					viewer: 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200',
				};

				const roleLabels = {
					admin: 'Admin',
					editor: 'Editor',
					viewer: 'Visualizador',
				};

				return (
					<span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${roleColors[value]}`}>
						{roleLabels[value]}
					</span>
				);
			},
		},
		{
			id: 'status',
			header: 'Status',
			accessor: 'status',
			width: '100px',
			align: 'center',
			render: (value) => (
				<span className={`
					inline-flex px-2 py-1 text-xs font-semibold rounded-full
					${value === 'active' 
						? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200' 
						: 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
					}
				`}>
					{value === 'active' ? 'Ativo' : 'Inativo'}
				</span>
			),
		},
		{
			id: 'createdAt',
			header: 'Criado em',
			accessor: 'createdAt',
			width: '150px',
			align: 'right',
			sortable: true,
			render: (value) => new Date(value).toLocaleDateString('pt-BR'),
		},
		{
			id: 'lastLogin',
			header: 'Último Login',
			accessor: 'lastLogin',
			width: '150px',
			align: 'right',
		},
	], []);

	return (
		<div className="space-y-6">
			{/* Header com controles */}
			<div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
				<div>
					<h2 className="text-2xl font-bold text-gray-900 dark:text-white">
						Gerenciamento de Usuários
					</h2>
					<p className="text-gray-600 dark:text-gray-400">
						{stats.filtersApplied 
							? `${stats.filtered} de ${stats.total} usuários (${stats.filterReduction}% filtrados)`
							: `${stats.total} usuários total`
						}
					</p>
				</div>

				<div className="flex items-center gap-3">
					<button
						onClick={() => exportData('csv')}
						className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 dark:bg-gray-800 dark:text-gray-300 dark:border-gray-600 dark:hover:bg-gray-700"
					>
						Exportar CSV
					</button>
					<button
						onClick={() => exportData('json')}
						className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 dark:bg-gray-800 dark:text-gray-300 dark:border-gray-600 dark:hover:bg-gray-700"
					>
						Exportar JSON
					</button>
				</div>
			</div>

			{/* Filtros */}
			<div className="bg-white dark:bg-gray-800 p-4 rounded-lg shadow space-y-4">
				<div className="grid grid-cols-1 md:grid-cols-3 gap-4">
					{/* Busca */}
					<div>
						<label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
							Buscar
						</label>
						<input
							type="text"
							value={filters.search}
							onChange={(e) => handleSearch(e.target.value)}
							placeholder="Nome, email ou departamento..."
							className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-indigo-500 focus:border-indigo-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
						/>
					</div>

					{/* Filtro por Status */}
					<div>
						<label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
							Status
						</label>
						<select
							value={filters.status || ''}
							onChange={(e) => handleFilter('status', e.target.value || undefined)}
							className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-indigo-500 focus:border-indigo-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
						>
							<option value="">Todos</option>
							<option value="active">Ativo</option>
							<option value="inactive">Inativo</option>
						</select>
					</div>

					{/* Botão limpar filtros */}
					<div className="flex items-end">
						<button
							onClick={clearFilters}
							disabled={!stats.filtersApplied}
							className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 border border-gray-300 rounded-md hover:bg-gray-200 disabled:opacity-50 disabled:cursor-not-allowed dark:bg-gray-700 dark:text-gray-300 dark:border-gray-600 dark:hover:bg-gray-600"
						>
							Limpar Filtros
						</button>
					</div>
				</div>
			</div>

			{/* Tabela */}
			<div className="bg-white dark:bg-gray-800 rounded-lg shadow">
				<DraggableTable
					columns={columns}
					data={data}
					onRowsReorder={handleRowsReorder}
					loading={loading}
					enableVirtualization={false} // Desabilitado para paginação
					stickyHeader={true}
					accessibility={{
						tableLabel: 'Tabela de usuários com drag and drop',
						dragInstructions: 'Use as setas do teclado para navegar. Pressione espaço para selecionar e mover uma linha.',
						announceReorder: (fromIndex, toIndex) => 
							`Usuário movido da posição ${fromIndex + 1} para a posição ${toIndex + 1}`,
					}}
				/>
			</div>

			{/* Paginação */}
			<div className="bg-white dark:bg-gray-800 px-4 py-3 rounded-lg shadow flex items-center justify-between">
				<div className="text-sm text-gray-700 dark:text-gray-300">
					Mostrando {pagination.startItem} a {pagination.endItem} de {pagination.totalItems} resultados
				</div>

				<div className="flex items-center space-x-2">
					<button
						onClick={() => handlePageChange(pagination.currentPage - 1)}
						disabled={!pagination.hasPreviousPage}
						className="px-3 py-1 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed dark:bg-gray-800 dark:text-gray-300 dark:border-gray-600 dark:hover:bg-gray-700"
					>
						Anterior
					</button>

					<span className="text-sm text-gray-700 dark:text-gray-300">
						Página {pagination.currentPage} de {pagination.totalPages}
					</span>

					<button
						onClick={() => handlePageChange(pagination.currentPage + 1)}
						disabled={!pagination.hasNextPage}
						className="px-3 py-1 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed dark:bg-gray-800 dark:text-gray-300 dark:border-gray-600 dark:hover:bg-gray-700"
					>
						Próxima
					</button>
				</div>
			</div>
		</div>
	);
}
