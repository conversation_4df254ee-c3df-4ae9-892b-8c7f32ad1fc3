import { useState, useCallback, useMemo, useEffect } from 'react';
import { useDebounce } from '@/hooks/useDebounce'; // Hook de debounce customizado
import type { TableColumn, TableRowType } from '../types';

interface UseAdvancedTableOptions<T> {
	initialData: TableRowType<T>[];
	searchableFields?: (keyof T)[];
	sortableFields?: (keyof T)[];
	pageSize?: number;
	enablePersistence?: boolean;
	persistenceKey?: string;
}

interface SortConfig<T> {
	field: keyof T;
	direction: 'asc' | 'desc';
}

interface FilterConfig {
	search: string;
	status?: string;
	dateRange?: {
		start: Date;
		end: Date;
	};
}

/**
 * Hook avançado para gerenciar estado complexo de tabelas
 * Inclui: busca, ordenação, paginação, filtros e persistência
 */
export function useAdvancedTable<T>({
	initialData,
	searchableFields = [],
	sortableFields = [],
	pageSize = 50,
	enablePersistence = false,
	persistenceKey = 'table-state',
}: UseAdvancedTableOptions<T>) {
	// Estados principais
	const [data, setData] = useState<TableRowType<T>[]>(initialData);
	const [loading, setLoading] = useState(false);
	const [currentPage, setCurrentPage] = useState(1);
	const [sortConfig, setSortConfig] = useState<SortConfig<T> | null>(null);
	const [filters, setFilters] = useState<FilterConfig>({
		search: '',
	});

	// Debounce da busca para performance
	const debouncedSearch = useDebounce(filters.search, 300);

	// Persistência do estado (localStorage)
	useEffect(() => {
		if (enablePersistence) {
			const savedState = localStorage.getItem(persistenceKey);
			if (savedState) {
				try {
					const { sortConfig: savedSort, filters: savedFilters, currentPage: savedPage } = JSON.parse(savedState);
					setSortConfig(savedSort);
					setFilters(savedFilters);
					setCurrentPage(savedPage);
				} catch (error) {
					console.warn('Erro ao carregar estado salvo da tabela:', error);
				}
			}
		}
	}, [enablePersistence, persistenceKey]);

	// Salva estado quando há mudanças
	useEffect(() => {
		if (enablePersistence) {
			const stateToSave = {
				sortConfig,
				filters,
				currentPage,
			};
			localStorage.setItem(persistenceKey, JSON.stringify(stateToSave));
		}
	}, [sortConfig, filters, currentPage, enablePersistence, persistenceKey]);

	// Função de busca
	const searchData = useCallback((data: TableRowType<T>[], searchTerm: string) => {
		if (!searchTerm.trim()) return data;

		return data.filter((row) => {
			return searchableFields.some((field) => {
				const value = row.data[field];
				return String(value).toLowerCase().includes(searchTerm.toLowerCase());
			});
		});
	}, [searchableFields]);

	// Função de ordenação
	const sortData = useCallback((data: TableRowType<T>[], config: SortConfig<T>) => {
		return [...data].sort((a, b) => {
			const aValue = a.data[config.field];
			const bValue = b.data[config.field];

			if (aValue === bValue) return 0;

			const comparison = aValue < bValue ? -1 : 1;
			return config.direction === 'desc' ? -comparison : comparison;
		});
	}, []);

	// Dados processados (busca, filtros, ordenação)
	const processedData = useMemo(() => {
		let result = [...data];

		// Aplicar busca
		if (debouncedSearch) {
			result = searchData(result, debouncedSearch);
		}

		// Aplicar filtros adicionais
		if (filters.status) {
			result = result.filter((row) => (row.data as any).status === filters.status);
		}

		if (filters.dateRange) {
			result = result.filter((row) => {
				const rowDate = new Date((row.data as any).createdAt);
				return rowDate >= filters.dateRange!.start && rowDate <= filters.dateRange!.end;
			});
		}

		// Aplicar ordenação
		if (sortConfig) {
			result = sortData(result, sortConfig);
		}

		return result;
	}, [data, debouncedSearch, filters, sortConfig, searchData, sortData]);

	// Paginação
	const paginatedData = useMemo(() => {
		const startIndex = (currentPage - 1) * pageSize;
		const endIndex = startIndex + pageSize;
		return processedData.slice(startIndex, endIndex);
	}, [processedData, currentPage, pageSize]);

	// Informações de paginação
	const paginationInfo = useMemo(() => {
		const totalItems = processedData.length;
		const totalPages = Math.ceil(totalItems / pageSize);
		const startItem = (currentPage - 1) * pageSize + 1;
		const endItem = Math.min(currentPage * pageSize, totalItems);

		return {
			totalItems,
			totalPages,
			currentPage,
			startItem,
			endItem,
			hasNextPage: currentPage < totalPages,
			hasPreviousPage: currentPage > 1,
		};
	}, [processedData.length, pageSize, currentPage]);

	// Handlers
	const handleSort = useCallback((field: keyof T) => {
		if (!sortableFields.includes(field)) return;

		setSortConfig((current) => {
			if (current?.field === field) {
				// Alternar direção ou remover ordenação
				if (current.direction === 'asc') {
					return { field, direction: 'desc' };
				} else {
					return null; // Remove ordenação
				}
			} else {
				return { field, direction: 'asc' };
			}
		});

		// Reset para primeira página ao ordenar
		setCurrentPage(1);
	}, [sortableFields]);

	const handleSearch = useCallback((searchTerm: string) => {
		setFilters((current) => ({ ...current, search: searchTerm }));
		setCurrentPage(1); // Reset para primeira página
	}, []);

	const handleFilter = useCallback((filterKey: keyof FilterConfig, value: any) => {
		setFilters((current) => ({ ...current, [filterKey]: value }));
		setCurrentPage(1);
	}, []);

	const handlePageChange = useCallback((page: number) => {
		setCurrentPage(page);
	}, []);

	const handleRowsReorder = useCallback((newRows: TableRowType<T>[]) => {
		// Atualizar apenas os dados da página atual
		const startIndex = (currentPage - 1) * pageSize;
		const newData = [...data];
		
		newRows.forEach((row, index) => {
			const globalIndex = startIndex + index;
			if (globalIndex < newData.length) {
				newData[globalIndex] = row;
			}
		});

		setData(newData);
	}, [data, currentPage, pageSize]);

	const refreshData = useCallback(async (newData?: TableRowType<T>[]) => {
		setLoading(true);
		try {
			if (newData) {
				setData(newData);
			}
			// Aqui você poderia fazer uma chamada à API
			// const freshData = await fetchTableData();
			// setData(freshData);
		} catch (error) {
			console.error('Erro ao atualizar dados:', error);
		} finally {
			setLoading(false);
		}
	}, []);

	const exportData = useCallback((format: 'csv' | 'json' = 'csv') => {
		const dataToExport = processedData.map((row) => row.data);
		
		if (format === 'csv') {
			// Implementação básica de CSV
			const headers = Object.keys(dataToExport[0] || {});
			const csvContent = [
				headers.join(','),
				...dataToExport.map((row) => 
					headers.map((header) => `"${(row as any)[header] || ''}"`).join(',')
				),
			].join('\n');

			const blob = new Blob([csvContent], { type: 'text/csv' });
			const url = URL.createObjectURL(blob);
			const link = document.createElement('a');
			link.href = url;
			link.download = `table-export-${new Date().toISOString().split('T')[0]}.csv`;
			link.click();
			URL.revokeObjectURL(url);
		} else {
			// Export JSON
			const jsonContent = JSON.stringify(dataToExport, null, 2);
			const blob = new Blob([jsonContent], { type: 'application/json' });
			const url = URL.createObjectURL(blob);
			const link = document.createElement('a');
			link.href = url;
			link.download = `table-export-${new Date().toISOString().split('T')[0]}.json`;
			link.click();
			URL.revokeObjectURL(url);
		}
	}, [processedData]);

	const clearFilters = useCallback(() => {
		setFilters({ search: '' });
		setSortConfig(null);
		setCurrentPage(1);
	}, []);

	// Estatísticas dos dados
	const stats = useMemo(() => {
		const total = data.length;
		const filtered = processedData.length;
		const filtersApplied = debouncedSearch || filters.status || filters.dateRange;

		return {
			total,
			filtered,
			filtersApplied: !!filtersApplied,
			filterReduction: filtersApplied ? ((total - filtered) / total * 100).toFixed(1) : 0,
		};
	}, [data.length, processedData.length, debouncedSearch, filters]);

	return {
		// Dados
		data: paginatedData,
		allData: processedData,
		loading,
		
		// Paginação
		pagination: paginationInfo,
		
		// Estados
		sortConfig,
		filters,
		stats,
		
		// Handlers
		handleSort,
		handleSearch,
		handleFilter,
		handlePageChange,
		handleRowsReorder,
		refreshData,
		exportData,
		clearFilters,
		
		// Utilitários
		isSearchable: (field: keyof T) => searchableFields.includes(field),
		isSortable: (field: keyof T) => sortableFields.includes(field),
		getSortDirection: (field: keyof T) => 
			sortConfig?.field === field ? sortConfig.direction : null,
	};
}
