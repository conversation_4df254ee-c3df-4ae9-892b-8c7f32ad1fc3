export default function Home() {
	return (
		<div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800 flex items-center justify-center p-8">
			<main className="max-w-4xl mx-auto text-center">
				<h1 className="text-4xl md:text-6xl font-bold text-gray-900 dark:text-white mb-6">Grid Forge</h1>
				<p className="text-xl md:text-2xl text-gray-700 dark:text-gray-300 mb-8 leading-relaxed">
					Componente de tabela React de alta performance com drag & drop nativo, virtualização automática e otimizações avançadas para
					aplicações modernas.
				</p>
				<a
					href="/table"
					className="inline-block bg-indigo-600 hover:bg-indigo-700 text-white font-semibold py-3 px-6 rounded-lg shadow-lg transition-colors"
				>
					Ver Demonstração da Tabela
				</a>
				<div className="grid md:grid-cols-3 gap-6 mt-12">
					<div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-lg">
						<h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">Ultra-Performático</h3>
						<p className="text-gray-600 dark:text-gray-400">Mantém 60fps mesmo com milhares de linhas, zero lag no drag and drop.</p>
					</div>
					<div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-lg">
						<h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">Flexível e Escalável</h3>
						<p className="text-gray-600 dark:text-gray-400">
							Virtualização automática e memoização inteligente para qualquer tamanho de dados.
						</p>
					</div>
					<div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-lg">
						<h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">Developer Experience</h3>
						<p className="text-gray-600 dark:text-gray-400">
							Construído com React, dnd-kit e otimizações avançadas para aplicações enterprise.
						</p>
					</div>
				</div>
			</main>
		</div>
	);
}
