export interface TableColumn<T = any> {
	id: string;
	header: string;
	accessor: keyof T | ((row: T) => any);
	width?: string | number;
	minWidth?: string | number;
	maxWidth?: string | number;
	sortable?: boolean;
	resizable?: boolean;
	align?: 'left' | 'center' | 'right';
	render?: (value: any, row: T, index: number) => React.ReactNode;
}

export interface TableRow<T = any> {
	id: string;
	data: T;
	disabled?: boolean;
}

export interface DragEndEvent {
	active: {
		id: string;
	};
	over: {
		id: string;
	} | null;
}

export interface TableState<T> {
	rows: TableRow<T>[];
	draggedRowId: string | null;
	isDragging: boolean;
}

export type TableAction<T> =
	| { type: 'SET_ROWS'; payload: TableRow<T>[] }
	| { type: 'REORDER_ROWS'; payload: { activeId: string; overId: string } }
	| { type: 'START_DRAG'; payload: string }
	| { type: 'END_DRAG' }
	| { type: 'UPDATE_ROW'; payload: { id: string; data: Partial<T> } }
	| { type: 'DELETE_ROW'; payload: string };

export interface DraggableTableProps<T> {
	columns: TableColumn<T>[];
	data: TableRow<T>[];
	onRowsReorder?: (newRows: TableRow<T>[]) => void;
	onRowUpdate?: (id: string, data: Partial<T>) => void;
	onRowDelete?: (id: string) => void;
	className?: string;
	rowClassName?: string | ((row: TableRow<T>, index: number) => string);
	cellClassName?: string | ((column: TableColumn<T>, row: TableRow<T>) => string);
	enableVirtualization?: boolean;
	virtualizationThreshold?: number;
	height?: string | number;
	stickyHeader?: boolean;
	loading?: boolean;
	emptyMessage?: string;
	dragHandleProps?: {
		className?: string;
		'aria-label'?: string;
	};
	accessibility?: {
		tableLabel?: string;
		dragInstructions?: string;
		announceReorder?: (fromIndex: number, toIndex: number) => string;
	};
}

export interface TableCellProps<T> {
	column: TableColumn<T>;
	row: TableRow<T>;
	rowIndex: number;
	className?: string;
}

export interface TableRowProps<T> {
	row: TableRow<T>;
	columns: TableColumn<T>[];
	index: number;
	isDragging: boolean;
	className?: string;
	cellClassName?: string | ((column: TableColumn<T>, row: TableRow<T>) => string);
}

export interface DragHandleProps {
	className?: string;
	'aria-label'?: string;
}
