"use client";

import { DndContext, DragOverlay } from "@dnd-kit/core";
import { SortableContext, verticalListSortingStrategy } from "@dnd-kit/sortable";
import { useVirtualizer } from "@tanstack/react-virtual";
import { useCallback, useMemo, useRef } from "react";
import { useDraggableTable } from "./hooks/useDraggableTable";
import { useTableState } from "./hooks/useTableState";
import { TableHeader } from "./TableHeader";
import { TableRow } from "./TableRow";
import type { DraggableTableProps } from "./types";

export function DraggableTable<T>({
	columns,
	data,
	onRowsReorder,
	onRowUpdate,
	onRowDelete,
	className = "",
	rowClassName = "",
	cellClassName = "",
	enableVirtualization = false,
	virtualizationThreshold = 100,
	height = "600px",
	stickyHeader = true,
	loading = false,
	emptyMessage = "Nenhum dado disponível",
	dragHandleProps,
	accessibility = {},
}: DraggableTableProps<T>) {
	const containerRef = useRef<HTMLDivElement>(null);

	const { state, rows, actions } = useTableState(data);

	// Determina se deve usar virtualização
	const shouldVirtualize = enableVirtualization && rows.length > virtualizationThreshold;

	// Configuração do virtualizador
	const virtualizer = useVirtualizer({
		count: rows.length,
		getScrollElement: () => containerRef.current,
		estimateSize: () => 60, // Altura estimada da linha
		enabled: shouldVirtualize,
	});

	// Handlers otimizados
	const handleReorder = useCallback(
		(activeId: string, overId: string) => {
			actions.reorderRows(activeId, overId);

			// Callback externo para sincronização
			if (onRowsReorder) {
				const newRows = [...state.rows];
				const oldIndex = newRows.findIndex(row => row.id === activeId);
				const newIndex = newRows.findIndex(row => row.id === overId);

				if (oldIndex !== -1 && newIndex !== -1) {
					const [movedRow] = newRows.splice(oldIndex, 1);
					newRows.splice(newIndex, 0, movedRow);
					onRowsReorder(newRows);
				}
			}

			// Anúncio para screen readers
			if (accessibility.announceReorder) {
				const oldIndex = rows.findIndex(row => row.id === activeId);
				const newIndex = rows.findIndex(row => row.id === overId);
				const announcement = accessibility.announceReorder(oldIndex, newIndex);

				// Criar anúncio acessível
				const announcer = document.createElement("div");
				announcer.setAttribute("aria-live", "polite");
				announcer.setAttribute("aria-atomic", "true");
				announcer.className = "sr-only";
				announcer.textContent = announcement;
				document.body.appendChild(announcer);

				setTimeout(() => document.body.removeChild(announcer), 1000);
			}
		},
		[actions, state.rows, onRowsReorder, accessibility.announceReorder, rows]
	);

	const handleDragStart = useCallback(
		(id: string) => {
			actions.startDrag(id);
		},
		[actions]
	);

	const handleDragEnd = useCallback(() => {
		actions.endDrag();
	}, [actions]);

	const {
		sensors,
		itemIds,
		handleDragStart: dndHandleDragStart,
		handleDragEnd: dndHandleDragEnd,
	} = useDraggableTable({
		rows,
		onReorder: handleReorder,
		onDragStart: handleDragStart,
		onDragEnd: handleDragEnd,
	});

	// Encontra a linha sendo arrastada para o overlay
	const draggedRow = useMemo(() => {
		return state.draggedRowId ? rows.find(row => row.id === state.draggedRowId) : null;
	}, [state.draggedRowId, rows]);

	// Renderização das linhas (com ou sem virtualização)
	const renderRows = () => {
		if (shouldVirtualize) {
			return virtualizer.getVirtualItems().map(virtualItem => {
				const row = rows[virtualItem.index];
				const resolvedRowClassName = typeof rowClassName === "function" ? rowClassName(row, virtualItem.index) : rowClassName;

				return (
					<div
						key={row.id}
						data-index={virtualItem.index}
						ref={virtualizer.measureElement}
						style={{
							position: "absolute",
							top: 0,
							left: 0,
							width: "100%",
							transform: `translateY(${virtualItem.start}px)`,
						}}
					>
						<TableRow
							row={row}
							columns={columns}
							index={virtualItem.index}
							isDragging={state.isDragging}
							className={resolvedRowClassName}
							cellClassName={cellClassName}
						/>
					</div>
				);
			});
		}

		return rows.map((row, index) => {
			const resolvedRowClassName = typeof rowClassName === "function" ? rowClassName(row, index) : rowClassName;

			return (
				<TableRow
					key={row.id}
					row={row}
					columns={columns}
					index={index}
					isDragging={state.isDragging}
					className={resolvedRowClassName}
					cellClassName={cellClassName}
				/>
			);
		});
	};

	if (loading) {
		return (
			<div className={`flex items-center justify-center p-8 ${className}`}>
				<div className="animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600"></div>
				<span className="ml-2 text-gray-600 dark:text-gray-400">Carregando...</span>
			</div>
		);
	}

	if (rows.length === 0) {
		return <div className={`text-center p-8 text-gray-500 dark:text-gray-400 ${className}`}>{emptyMessage}</div>;
	}

	return (
		<DndContext sensors={sensors} onDragStart={dndHandleDragStart} onDragEnd={dndHandleDragEnd}>
			<SortableContext items={itemIds} strategy={verticalListSortingStrategy}>
				<div
					className={`
						border border-gray-200 dark:border-gray-700
						rounded-lg overflow-hidden
						${className}
					`}
					role="grid"
					aria-label={accessibility.tableLabel || "Tabela interativa com drag and drop"}
					aria-rowcount={rows.length + 1}
					aria-colcount={columns.length + 1}
				>
					<TableHeader columns={columns} stickyHeader={stickyHeader} />

					<div ref={containerRef} className="overflow-auto" style={{ height: shouldVirtualize ? height : "auto" }}>
						{shouldVirtualize ? (
							<div
								style={{
									height: virtualizer.getTotalSize(),
									width: "100%",
									position: "relative",
								}}
							>
								{renderRows()}
							</div>
						) : (
							<div className="divide-y divide-gray-200 dark:divide-gray-700">{renderRows()}</div>
						)}
					</div>
				</div>

				<DragOverlay>
					{draggedRow ? (
						<div className="bg-white dark:bg-gray-800 shadow-lg rounded border opacity-90">
							<TableRow
								row={draggedRow}
								columns={columns}
								index={0}
								isDragging={true}
								className={typeof rowClassName === "function" ? rowClassName(draggedRow, 0) : rowClassName}
								cellClassName={cellClassName}
							/>
						</div>
					) : null}
				</DragOverlay>

				{/* Instruções de acessibilidade */}
				<div className="sr-only" aria-live="polite">
					{accessibility.dragInstructions ||
						"Use as setas do teclado para navegar. Pressione espaço para selecionar uma linha e mover com as setas. Pressione espaço novamente para soltar."}
				</div>
			</SortableContext>
		</DndContext>
	);
}
