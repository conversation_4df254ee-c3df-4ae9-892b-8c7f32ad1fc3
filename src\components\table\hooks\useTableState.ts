import { useReducer, useCallback, useMemo } from 'react';
import { arrayMove } from '@dnd-kit/sortable';
import type { TableState, TableAction, TableRow } from '../types';

function tableReducer<T>(state: TableState<T>, action: TableAction<T>): TableState<T> {
	switch (action.type) {
		case 'SET_ROWS':
			return {
				...state,
				rows: action.payload,
			};

		case 'REORDER_ROWS': {
			const { activeId, overId } = action.payload;
			const oldIndex = state.rows.findIndex((row) => row.id === activeId);
			const newIndex = state.rows.findIndex((row) => row.id === overId);

			if (oldIndex === -1 || newIndex === -1) return state;

			return {
				...state,
				rows: arrayMove(state.rows, oldIndex, newIndex),
				isDragging: false,
				draggedRowId: null,
			};
		}

		case 'START_DRAG':
			return {
				...state,
				isDragging: true,
				draggedRowId: action.payload,
			};

		case 'END_DRAG':
			return {
				...state,
				isDragging: false,
				draggedRowId: null,
			};

		case 'UPDATE_ROW': {
			const { id, data } = action.payload;
			return {
				...state,
				rows: state.rows.map((row) =>
					row.id === id ? { ...row, data: { ...row.data, ...data } } : row
				),
			};
		}

		case 'DELETE_ROW':
			return {
				...state,
				rows: state.rows.filter((row) => row.id !== action.payload),
			};

		default:
			return state;
	}
}

export function useTableState<T>(initialRows: TableRow<T>[]) {
	const [state, dispatch] = useReducer(tableReducer<T>, {
		rows: initialRows,
		draggedRowId: null,
		isDragging: false,
	});

	const setRows = useCallback((rows: TableRow<T>[]) => {
		dispatch({ type: 'SET_ROWS', payload: rows });
	}, []);

	const reorderRows = useCallback((activeId: string, overId: string) => {
		dispatch({ type: 'REORDER_ROWS', payload: { activeId, overId } });
	}, []);

	const startDrag = useCallback((id: string) => {
		dispatch({ type: 'START_DRAG', payload: id });
	}, []);

	const endDrag = useCallback(() => {
		dispatch({ type: 'END_DRAG' });
	}, []);

	const updateRow = useCallback((id: string, data: Partial<T>) => {
		dispatch({ type: 'UPDATE_ROW', payload: { id, data } });
	}, []);

	const deleteRow = useCallback((id: string) => {
		dispatch({ type: 'DELETE_ROW', payload: id });
	}, []);

	// Memoização dos dados processados para evitar recálculos desnecessários
	const memoizedRows = useMemo(() => state.rows, [state.rows]);
	
	const rowsById = useMemo(() => {
		return state.rows.reduce((acc, row) => {
			acc[row.id] = row;
			return acc;
		}, {} as Record<string, TableRow<T>>);
	}, [state.rows]);

	return {
		state,
		rows: memoizedRows,
		rowsById,
		actions: {
			setRows,
			reorderRows,
			startDrag,
			endDrag,
			updateRow,
			deleteRow,
		},
	};
}
