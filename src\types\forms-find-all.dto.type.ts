export interface IFormFindByIdDto {
	id: number;
	title: string;
	text: string;
	nomenclature: string;
	revision: string;
	developer: {
		id: string;
		name: string;
	};
	approver: {
		id: string;
		name: string;
	};
	fields: Field[];
	canUpdate: boolean;
}

export const InspectionFormTypeEnum = {
	INTEGER: 1,
	TEXT: 2,
	DATE: 3,
	HOUR: 4,
	SECONDS: 5,
	OPTIONS: 6,
	BOOLEAN: 7,
	DECIMAL: 8,
	DECIMAL2DIGITS: 9,
	IMAGE: 10,
} as const;

export type InspectionFormTypeEnum = (typeof InspectionFormTypeEnum)[keyof typeof InspectionFormTypeEnum];

export interface Field {
	id?: number;
	nickname: string;
	required: boolean;
	fieldType: InspectionFormGroup;
	field: InspectionFormGroup;
	measure: Measure;
	group: number;
	sequence: number;
	groupTitle: string;
	biFilter: boolean;
	options?: Option[];
}

interface Option {
	id: number;
	sequence: number;
	option: string;
}

interface Measure {
	id: number;
	name: string;
	abbreviation: string;
}

interface InspectionFormGroup {
	id: InspectionFormTypeEnum;
	name: string;
}
