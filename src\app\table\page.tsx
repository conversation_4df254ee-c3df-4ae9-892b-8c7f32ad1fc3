"use client";

import type { TableColumn, TableRowType } from "@/components/table";
import { DraggableTable } from "@/components/table";
import { useMemo, useState } from "react";

interface User {
	id: string;
	name: string;
	email: string;
	role: string;
	status: "active" | "inactive";
	lastLogin: string;
	avatar?: string;
}

const generateMockData = (count: number): TableRowType<User>[] => {
	const roles = ["Admin", "Editor", "Viewer", "Manager"];
	const statuses: ("active" | "inactive")[] = ["active", "inactive"];

	return Array.from({ length: count }, (_, i) => ({
		id: `user-${i + 1}`,
		data: {
			id: `user-${i + 1}`,
			name: `Usuário ${i + 1}`,
			email: `usuario${i + 1}@exemplo.com`,
			role: roles[Math.floor(Math.random() * roles.length)],
			status: statuses[Math.floor(Math.random() * statuses.length)],
			lastLogin: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000).toLocaleDateString("pt-BR"),
		},
	}));
};

export default function TablePage() {
	const [data, setData] = useState<TableRowType<User>[]>(() => generateMockData(50));
	const [enableVirtualization, setEnableVirtualization] = useState(false);

	const columns: TableColumn<User>[] = useMemo(
		() => [
			{
				id: "name",
				header: "Nome",
				accessor: "name",
				width: "200px",
				render: (value, row) => (
					<div className="flex items-center space-x-3">
						<div className="w-8 h-8 bg-indigo-100 dark:bg-indigo-900 rounded-full flex items-center justify-center">
							<span className="text-sm font-medium text-indigo-600 dark:text-indigo-400">{value.charAt(0)}</span>
						</div>
						<span className="font-medium">{value}</span>
					</div>
				),
			},
			{
				id: "email",
				header: "Email",
				accessor: "email",
				width: "250px",
			},
			{
				id: "role",
				header: "Função",
				accessor: "role",
				width: "120px",
				render: value => (
					<span
						className={`
					inline-flex px-2 py-1 text-xs font-semibold rounded-full
					${value === "Admin" ? "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200" : ""}
					${value === "Manager" ? "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200" : ""}
					${value === "Editor" ? "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200" : ""}
					${value === "Viewer" ? "bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200" : ""}
				`}
					>
						{value}
					</span>
				),
			},
			{
				id: "status",
				header: "Status",
				accessor: "status",
				width: "100px",
				align: "center",
				render: value => (
					<span
						className={`
					inline-flex px-2 py-1 text-xs font-semibold rounded-full
					${
						value === "active"
							? "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200"
							: "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200"
					}
				`}
					>
						{value === "active" ? "Ativo" : "Inativo"}
					</span>
				),
			},
			{
				id: "lastLogin",
				header: "Último Login",
				accessor: "lastLogin",
				width: "150px",
				align: "right",
			},
		],
		[]
	);

	const handleRowsReorder = (newRows: TableRowType<User>[]) => {
		setData(newRows);
	};

	const addMoreData = () => {
		const newData = generateMockData(50);
		setData(prev => [...prev, ...newData]);
	};

	return (
		<div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800 p-8">
			<main className="max-w-6xl mx-auto">
				<div className="text-center mb-8">
					<h1 className="text-3xl md:text-5xl font-bold text-gray-900 dark:text-white mb-4">Demonstração da Tabela</h1>
					<p className="text-lg text-gray-700 dark:text-gray-300 mb-6">
						Arraste e solte as linhas para reordenar. Esta tabela é ultra-performática e escalável.
					</p>

					{/* Controles */}
					<div className="flex flex-wrap justify-center gap-4 mb-8">
						<button onClick={addMoreData} className="px-4 py-2 bg-indigo-600 hover:bg-indigo-700 text-white rounded-lg transition-colors">
							Adicionar +50 linhas ({data.length} total)
						</button>

						<label className="flex items-center space-x-2 text-gray-700 dark:text-gray-300">
							<input
								type="checkbox"
								checked={enableVirtualization}
								onChange={e => setEnableVirtualization(e.target.checked)}
								className="rounded"
							/>
							<span>Ativar Virtualização</span>
						</label>
					</div>
				</div>

				{/* Tabela */}
				<div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg">
					<DraggableTable
						columns={columns}
						data={data}
						onRowsReorder={handleRowsReorder}
						enableVirtualization={enableVirtualization}
						virtualizationThreshold={100}
						height="500px"
						stickyHeader={true}
						accessibility={{
							tableLabel: "Tabela de usuários com drag and drop",
							dragInstructions: "Use as setas do teclado para navegar. Pressione espaço para selecionar e mover uma linha.",
							announceReorder: (fromIndex, toIndex) => `Linha movida da posição ${fromIndex + 1} para a posição ${toIndex + 1}`,
						}}
					/>
				</div>

				{/* Informações de Performance */}
				<div className="mt-8 grid md:grid-cols-3 gap-6">
					<div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow">
						<h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">Performance</h3>
						<p className="text-gray-600 dark:text-gray-400 text-sm">
							{data.length} linhas renderizadas com {enableVirtualization ? "virtualização ativa" : "renderização completa"}
						</p>
					</div>

					<div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow">
						<h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">Acessibilidade</h3>
						<p className="text-gray-600 dark:text-gray-400 text-sm">Suporte completo a teclado, screen readers e ARIA labels</p>
					</div>

					<div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow">
						<h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">Responsividade</h3>
						<p className="text-gray-600 dark:text-gray-400 text-sm">Layout adaptativo com scroll horizontal em dispositivos móveis</p>
					</div>
				</div>
			</main>
		</div>
	);
}
