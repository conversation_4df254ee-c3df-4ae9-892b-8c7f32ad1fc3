import React from 'react';
import type { TableCellProps } from './types';

export const TableCell = React.memo<TableCellProps<any>>(
	({ column, row, rowIndex, className = '' }) => {
		const value = typeof column.accessor === 'function' 
			? column.accessor(row.data) 
			: row.data[column.accessor];

		const content = column.render 
			? column.render(value, row.data, rowIndex) 
			: value;

		const alignmentClass = {
			left: 'text-left',
			center: 'text-center',
			right: 'text-right',
		}[column.align || 'left'];

		return (
			<div
				className={`
					px-4 py-3 
					text-sm text-gray-900 dark:text-gray-100
					border-b border-gray-200 dark:border-gray-700
					${alignmentClass}
					${className}
				`}
				role="gridcell"
				style={{
					width: column.width,
					minWidth: column.minWidth,
					maxWidth: column.maxWidth,
				}}
			>
				{content}
			</div>
		);
	}
);

TableCell.displayName = 'TableCell';
